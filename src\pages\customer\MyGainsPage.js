import React, { useState, useEffect, useMemo } from 'react';
import { useNavigate } from 'react-router-dom';
import { Container, Row, Col, Button, Form, Card } from 'react-bootstrap';
import { DataGrid } from '@mui/x-data-grid';
import { Box, Chip, TextField, MenuItem, IconButton } from '@mui/material';
import { Download as DownloadIcon, FilterAlt as FilterIcon } from '@mui/icons-material';
import { getSupabase } from '../../supabaseClient';
import { useTranslation } from 'react-i18next';
import { zhCN, jaJP, enUS } from '@mui/x-data-grid/locales';

const MyGainsPage = () => {
    const { t, i18n } = useTranslation();
    const navigate = useNavigate();
    const [transactions, setTransactions] = useState([]);
    const [loading, setLoading] = useState(true);
    const [orders, setOrders] = useState([]);
    const [selectedMonth, setSelectedMonth] = useState('all');
    const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());

    // Get MUI DataGrid locale based on current language
    const getDataGridLocale = () => {
        switch (i18n.language) {
            case 'zh':
                return zhCN.components.MuiDataGrid.defaultProps.localeText;
            case 'ja':
                return jaJP.components.MuiDataGrid.defaultProps.localeText;
            default:
                return enUS.components.MuiDataGrid.defaultProps.localeText;
        }
    };

    const handleNavigation = (path) => {
        console.log('Navigating to:', path);
        console.log('Current URL:', window.location.href);
        navigate(path);
    };


    // Get available months and years from transactions data
    const availableMonths = useMemo(() => {
        const months = new Set();
        const years = new Set();

        transactions.forEach(transaction => {
            const date = new Date(transaction.tx_date);
            months.add(date.getMonth());
            years.add(date.getFullYear());
        });

        return {
            months: Array.from(months).sort((a, b) => a - b),
            years: Array.from(years).sort((a, b) => b - a)
        };
    }, [transactions]);

    // Filter transactions based on selected month and year
    const filteredTransactions = useMemo(() => {
        if (selectedMonth === 'all') return transactions;

        return transactions.filter(transaction => {
            const date = new Date(transaction.tx_date);
            const month = date.getMonth();
            const year = date.getFullYear();

            return month === parseInt(selectedMonth) && year === selectedYear;
        });
    }, [transactions, selectedMonth, selectedYear]);

    // Filter orders based on selected month and year
    const filteredOrders = useMemo(() => {
        if (selectedMonth === 'all') return orders;
        
        return orders.filter(order => {
            const date = new Date(order.created_at);
            const month = date.getMonth();
            const year = date.getFullYear();
            
            return month === parseInt(selectedMonth) && year === selectedYear;
        });
    }, [orders, selectedMonth, selectedYear]);

    // CSV Export function with proper encoding and formatting
    const exportToCSV = (data, filename, isTransactions = true) => {
        if (data.length === 0) {
            alert(t('no_data_to_export') || 'No data to export');
            return;
        }

        let csvContent = '';

        if (isTransactions) {
            // Transactions CSV headers
            const headers = [
                t('date') || 'Date',
                t('order_id') || 'Order ID',
                t('gain_amount') || 'Gain Amount',
                t('tx_type') || 'Transaction Type'
            ];
            csvContent = headers.join(',') + '\n';

            // Transactions CSV data
            data.forEach(row => {
                const formatDate = (dateString) => {
                    const date = new Date(dateString);
                    return date.getFullYear() + '-' +
                           String(date.getMonth() + 1).padStart(2, '0') + '-' +
                           String(date.getDate()).padStart(2, '0');
                };

                const rowData = [
                    `"${formatDate(row.tx_date)}"`,
                    `"${row.order_id || 'N/A'}"`,
                    row.amount_net || 0,
                    `"${t(row.tx_type) || row.tx_type}"`
                ];
                csvContent += rowData.join(',') + '\n';
            });
        } else {
            // Orders CSV headers
            const headers = [
                t('order_id') || 'Order ID',
                t('product_name') || 'Product Name',
                t('shares') || 'Shares',
                t('storage_cost') || 'Storage Cost',
                t('pledge_cost') || 'Pledge Cost',
                t('total_rate') || 'Total Rate',
                t('start_date') || 'Start Date',
                t('end_date') || 'End Date',
                t('status') || 'Status',
                t('created_at') || 'Created At'
            ];
            csvContent = headers.join(',') + '\n';
            
            // Orders CSV data
            data.forEach(row => {
                const formatDate = (dateString) => {
                    const date = new Date(dateString);
                    return date.getFullYear() + '-' + 
                           String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                           String(date.getDate()).padStart(2, '0');
                };

                const formatDateTime = (dateString) => {
                    const date = new Date(dateString);
                    return date.getFullYear() + '-' + 
                           String(date.getMonth() + 1).padStart(2, '0') + '-' + 
                           String(date.getDate()).padStart(2, '0') + ' ' +
                           String(date.getHours()).padStart(2, '0') + ':' + 
                           String(date.getMinutes()).padStart(2, '0');
                };

                const rowData = [
                    `"${row.id}"`,
                    `"${row.products?.name || 'N/A'}"`,
                    row.shares || 0,
                    row.storage_cost || 0,
                    row.pledge_cost || 0,
                    row.total_rate || 0,
                    `"${formatDate(row.start_at)}"`,
                    `"${formatDate(row.end_at)}"`,
                    `"${t(row.review_status) || row.review_status}"`,
                    `"${formatDateTime(row.created_at)}"`
                ];
                csvContent += rowData.join(',') + '\n';
            });
        }

        // Add BOM for UTF-8 encoding to handle special characters properly
        const BOM = '\uFEFF';
        const csvContentWithBOM = BOM + csvContent;

        // Create and download CSV file with proper encoding
        const blob = new Blob([csvContentWithBOM], { 
            type: 'text/csv;charset=utf-8;' 
        });
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', filename);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);
    };

    const handleExportTransactions = () => {
        const currentDate = new Date();
        const dateString = currentDate.toISOString().split('T')[0];
        const monthFilter = selectedMonth === 'all' ? 'all' : `${selectedYear}-${String(parseInt(selectedMonth) + 1).padStart(2, '0')}`;
        const filename = `transactions_${monthFilter}_${dateString}.csv`;
        exportToCSV(filteredTransactions, filename, true);
    };

    const handleExportOrders = () => {
        const currentDate = new Date();
        const dateString = currentDate.toISOString().split('T')[0];
        const monthFilter = selectedMonth === 'all' ? 'all' : `${selectedYear}-${String(parseInt(selectedMonth) + 1).padStart(2, '0')}`;
        const filename = `orders_${monthFilter}_${dateString}.csv`;
        exportToCSV(filteredOrders, filename, false);
    };

    // Month names for display
    const getMonthName = (monthIndex) => {
        const date = new Date();
        date.setMonth(monthIndex);
        return date.toLocaleDateString(i18n.language === 'zh' ? 'zh-CN' : i18n.language === 'ja' ? 'ja-JP' : 'en-US', { month: 'long' });
    };

    useEffect(() => {
        const fetchTransactions = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            const { data, error } = await supabase
                .from('transactions')
                .select(`
                    id,
                    tx_date,
                    order_id,
                    receiver_user_id,
                    amount_net,
                    tx_type,
                    created_at
                `)
                .eq('receiver_user_id', user.id)
                .in('tx_type', ['order_distributions', 'vesting_release_daily'])
                .order('tx_date', { ascending: false });

            if (error) {
                console.error('Error fetching transactions:', error);
            } else {
                setTransactions(data);
            }
            setLoading(false);

        };

        const fetchOrders = async () => {
            const supabase = getSupabase();
            if (!supabase) return;

            setLoading(true);
            const { data: { user } } = await supabase.auth.getUser();

            if (!user) {
                setLoading(false);
                return; // User not logged in
            }

            const { data, error } = await supabase
                .from('orders')
                .select(`
                            id,
                            shares,
                            storage_cost,
                            pledge_cost,
                            total_rate,
                            start_at,
                            end_at,
                            review_status,
                            created_at,
                            products ( name )
                        `)
                .eq('customer_id', user.id)
                .is('deleted_at', null)
                .order('created_at', { ascending: false });

            if (error) {
                console.error('Error fetching orders:', error);
            } else {
                setOrders(data);
            }
            setLoading(false);
        };

        fetchOrders();
        fetchTransactions();
    }, []);

    // Define columns for transactions DataGrid with wider columns
    const transactionsColumns = [
        {
            field: 'tx_date',
            headerName: t('date'),
            width: 200, // 增加宽度从 150 到 200
            renderCell: (params) => {
                const date = new Date(params.value);
                return date.getFullYear() + '-' +
                       String(date.getMonth() + 1).padStart(2, '0') + '-' +
                       String(date.getDate()).padStart(2, '0');
            }
        },
        {
            field: 'order_id',
            headerName: t('order_id'),
            width: 250, // 增加宽度从 150 到 250，用于显示更多字符
            renderCell: (params) => params.value ? params.value.substring(0, 12) + '...' : 'N/A' // 显示更多字符
        },
        {
            field: 'amount_net',
            headerName: t('gain_amount'),
            width: 180, // 增加宽度从 150 到 180
            type: 'number',
            renderCell: (params) => `${params.value || 0} FIL`
        },
        {
            field: 'tx_type',
            headerName: t('tx_type'),
            width: 220, // 增加宽度从 180 到 220
            renderCell: (params) => (
                <Chip
                    label={t(params.value) || params.value}
                    color={params.value === 'order_distributions' ? 'success' : 'info'}
                    size="small"
                />
            )
        }
    ];

    // Define columns for orders DataGrid
    const ordersColumns = [
        {
            field: 'id',
            headerName: t('order_id'),
            width: 150,
            renderCell: (params) => params.value.substring(0, 8) + '...'
        },
        {
            field: 'product_name',
            headerName: t('product_name'),
            width: 200,
            renderCell: (params) => params.row.products?.name || 'N/A'
        },
        {
            field: 'shares',
            headerName: t('shares'),
            width: 120,
            type: 'number'
        },
        {
            field: 'storage_cost',
            headerName: t('storage_cost'),
            width: 150,
            type: 'number'
        },
        {
            field: 'pledge_cost',
            headerName: t('pledge_cost'),
            width: 150,
            type: 'number'
        },
        {
            field: 'total_rate',
            headerName: t('total_rate'),
            width: 120,
            type: 'number'
        },
        {
            field: 'start_at',
            headerName: t('start_date'),
            width: 150,
            renderCell: (params) => new Date(params.value).toLocaleDateString()
        },
        {
            field: 'end_at',
            headerName: t('end_date'),
            width: 150,
            renderCell: (params) => new Date(params.value).toLocaleDateString()
        },
        {
            field: 'review_status',
            headerName: t('status'),
            width: 120,
            renderCell: (params) => (
                <Chip
                    label={t(params.value)}
                    color={params.value === 'approved' ? 'success' : 'warning'}
                    size="small"
                />
            )
        },
        {
            field: 'created_at',
            headerName: t('created_at'),
            width: 180,
            renderCell: (params) => new Date(params.value).toLocaleString()
        }
    ];

    if (loading) {
        return <div>{t('loading_transactions') || 'Loading transactions...'}</div>;
    }

    return (
            <Container>
                <Row className="justify-content-center">
                    <Col lg={10} xl={8}>
                        <Card
                            className="border-0 shadow-sm rounded-4"
                        >
                            <Card.Body className="p-4">
                            <Row className="align-items-center g-3">
                                {/* 左侧：标题 */}
                                <Col md={8}>
                                <div className="d-flex align-items-center gap-2">
                                    <span style={{ fontSize: 28, lineHeight: 1 }}>➤</span>
                                    <h4 className="m-0">{t('buy_power')}</h4>
                                </div>
                                </Col>

                                {/* 右侧：按钮 */}
                                <Col md={4} className="text-md-end">
                                <Button
                                    variant="success"
                                    className="px-4 py-2 shadow-sm rounded-3"
                                    onClick={() => handleNavigation('/products')}
                                >
                                    {t('browse_products')}
                                </Button>
                                </Col>
                            </Row>
                            </Card.Body>
                        </Card>
                    </Col>
                </Row>
                <h2 className="mb-4">{t('my_gains')}</h2>
                
                {/* Filter Controls */}
                <Row className="mb-3 mt-1">
                    <Col md={3}>
                        <TextField
                            select
                            label={t('select_year') || 'Select Year'}
                            value={selectedYear}
                            onChange={(e) => setSelectedYear(parseInt(e.target.value))}
                            size="small"
                            fullWidth
                            variant="outlined"
                        >
                            {availableMonths.years.map((year) => (
                                <MenuItem key={year} value={year}>
                                    {year}
                                </MenuItem>
                            ))}
                        </TextField>
                    </Col>
                    <Col md={3}>
                        <TextField
                            select
                            label={t('select_month') || 'Select Month'}
                            value={selectedMonth}
                            onChange={(e) => setSelectedMonth(e.target.value)}
                            size="small"
                            fullWidth
                            variant="outlined"
                        >
                            <MenuItem value="all">{t('all_months') || 'All Months'}</MenuItem>
                            {availableMonths.months.map((monthIndex) => (
                                <MenuItem key={monthIndex} value={monthIndex}>
                                    {getMonthName(monthIndex)}
                                </MenuItem>
                            ))}
                        </TextField>
                    </Col>
                    <Col md={6} className="d-flex align-items-end">
                        <Button
                            variant="outline-primary"
                            onClick={handleExportTransactions}
                            className="me-2"
                            disabled={filteredTransactions.length === 0}
                        >
                            <DownloadIcon style={{ marginRight: 8 }} />
                            {t('export_transactions_csv') || 'Export Transactions CSV'}
                        </Button>
                        <Button
                            variant="outline-secondary"
                            onClick={handleExportOrders}
                            disabled={filteredOrders.length === 0}
                        >
                            <DownloadIcon style={{ marginRight: 8 }} />
                            {t('export_orders_csv') || 'Export Orders CSV'}
                        </Button>
                    </Col>
                </Row>

                {/* Filter Summary */}
                <Row className="mb-3">
                    <Col>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <FilterIcon />
                            <span>
                                {selectedMonth === 'all'
                                    ? `${t('showing_all_data') || 'Showing all data'} (${selectedYear})`
                                    : `${t('showing_data_for') || 'Showing data for'} ${getMonthName(parseInt(selectedMonth))} ${selectedYear}`
                                }
                            </span>
                            <Chip
                                label={`${filteredTransactions.length} ${t('transactions') || 'transactions'}, ${filteredOrders.length} ${t('orders') || 'orders'}`}
                                size="small"
                                color="info"
                            />
                        </Box>
                    </Col>
                </Row>

                {/* Transactions DataGrid */}
                <Row>
                    <Col>
                        <Box sx={{ height: 400, width: '100%', mb: 4 }}>
                            <DataGrid
                                rows={filteredTransactions}
                                columns={transactionsColumns}
                                initialState={{
                                    pagination: {
                                        paginationModel: { page: 0, pageSize: 10 },
                                    },
                                }}
                                pageSizeOptions={[5, 10, 20]}
                                disableRowSelectionOnClick
                                loading={loading}
                                localeText={{
                                    ...getDataGridLocale(),
                                    noRowsLabel: selectedMonth === 'all'
                                        ? t('no_transactions_record') || 'No transactions record'
                                        : t('no_transactions_for_selected_period') || 'No transactions for selected period'
                                }}
                                sx={{
                                    '& .MuiDataGrid-cell': {
                                        borderBottom: '1px solid #e0e0e0',
                                    },
                                    '& .MuiDataGrid-columnHeaders': {
                                        backgroundColor: '#f5f5f5',
                                        borderBottom: '2px solid #e0e0e0',
                                    },
                                    '& .MuiDataGrid-row:hover': {
                                        backgroundColor: '#f9f9f9',
                                    }
                                }}
                            />
                        </Box>
                    </Col>
                </Row>

                {/* Orders Section */}
                <h2 className="mt-3 mb-4">{t('orders')}</h2>
                <Row>
                    <Col>
                        <Box sx={{ height: 300, width: '100%' }}>
                            <DataGrid
                                rows={filteredOrders}
                                columns={ordersColumns}
                                initialState={{
                                    pagination: {
                                        paginationModel: { page: 0, pageSize: 5 },
                                    },
                                }}
                                pageSizeOptions={[5, 10, 20, 50]}
                                disableRowSelectionOnClick
                                loading={loading}
                                localeText={{
                                    ...getDataGridLocale(),
                                    noRowsLabel: selectedMonth === 'all' 
                                        ? t('no_orders') 
                                        : t('no_orders_for_selected_period') || 'No orders for selected period'
                                }}
                                sx={{
                                    '& .MuiDataGrid-cell': {
                                        borderBottom: '1px solid #e0e0e0',
                                    },
                                    '& .MuiDataGrid-columnHeaders': {
                                        backgroundColor: '#f5f5f5',
                                        borderBottom: '2px solid #e0e0e0',
                                    },
                                    '& .MuiDataGrid-row:hover': {
                                        backgroundColor: '#f9f9f9',
                                    }
                                }}
                            />
                        </Box>
                    </Col>
                </Row>
            </Container>
    );
};

export default MyGainsPage;